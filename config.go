package main

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
)

// Config 存储全局配置信息
type Config struct {
	Credentials [][2]string `json:"credentials"`
	Ports       []string    `json:"ports"`
	Files       FileConfig
	Options     TestOptions
}

// FileConfig 存储文件相关配置
type FileConfig struct {
	Input   string
	Outputs map[string]string // 输出文件映射
}

// TestOptions 存储测试相关选项
type TestOptions struct {
	ThreadLimit    int
	TimeoutSeconds int
	OutputFormats  map[string]bool
}

// LoadConfig 从文件加载配置
func LoadConfig(filename string) (*Config, error) {
	file, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("无法读取配置文件: %v", err)
	}

	var conf Config
	if err := json.Unmarshal(file, &conf); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &conf, nil
}

// LoadDefaultConfig 加载默认配置
func LoadDefaultConfig() *Config {
	cfg := &Config{
		Credentials: [][2]string{
			{"admin", "admin"},
			{"root", "root"},
		},
		Ports: []string{"1080", "8888"},
		Options: TestOptions{
			ThreadLimit:    10,
			TimeoutSeconds: 3,
		},
	}

	// 初始化文件配置
	cfg.Files.Input = "ip_output2.txt"
	cfg.Files.Outputs = map[string]string{
		"3":      "original.txt",
		"30":     "delay.txt",
		"multi":  "duplicate.txt",
		"noauth": "noauth.txt",
	}

	// 初始化输出格式
	cfg.Options.OutputFormats = map[string]bool{
		"3":      false,
		"30":     false,
		"multi":  false,
		"noauth": false,
	}

	return cfg
}

// SaveResults 保存测试结果到文件
func SaveResults(cfg *Config, results *TestResults) error {
	if err := saveToFile(cfg.Files.Outputs["3"], results.Normal, false); err != nil {
		return fmt.Errorf("保存原格式输出失败: %v", err)
	}

	if err := saveToFile(cfg.Files.Outputs["30"], results.Normal, true); err != nil {
		return fmt.Errorf("保存带延迟输出失败: %v", err)
	}

	if len(results.Multi) > 0 {
		if err := saveToFile(cfg.Files.Outputs["multi"], results.Multi, true); err != nil {
			return fmt.Errorf("保存多账号输出失败: %v", err)
		}
	}

	if len(results.NoAuth) > 0 {
		if err := saveToFile(cfg.Files.Outputs["noauth"], results.NoAuth, false); err != nil {
			return fmt.Errorf("保存无认证输出失败: %v", err)
		}
	}

	return nil
}

// saveToFile 将代理信息保存到文件
func saveToFile(filename string, proxies []ProxyInfo, includeDelay bool) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	for _, proxy := range proxies {
		var line string
		if len(strings.TrimSpace(proxy.User)) == 0 {
			line = fmt.Sprintf("socks5://%s:%s\n", proxy.IP, proxy.Port)
		} else if includeDelay {
			line = fmt.Sprintf("socks5://%s:%s@%s:%s (latency: %v)\n",
				proxy.User, proxy.Password, proxy.IP, proxy.Port, proxy.Latency)
		} else {
			line = fmt.Sprintf("socks5://%s:%s@%s:%s\n",
				proxy.User, proxy.Password, proxy.IP, proxy.Port)
		}
		if _, err := file.WriteString(line); err != nil {
			return err
		}
	}

	return nil
}
