package main

import (
    "encoding/json"
    "fmt"
    "os"
)

// LoadConfig 从文件加载配置
func LoadConfig(filename string) (*Config, error) {
    file, err := os.ReadFile(filename)
    if err != nil {
        return nil, fmt.Errorf("无法读取配置文件: %v", err)
    }

    var conf Config
    if err := json.Unmarshal(file, &conf); err != nil {
        return nil, fmt.Errorf("解析配置文件失败: %v", err)
    }

    return &conf, nil
}

// LoadDefaultConfig 加载默认配置
func LoadDefaultConfig() *Config {
    cfg := &Config{
        Credentials: [][2]string{
            {"admin", "admin"},
            {"root", "root"},
            {"user", "pass"},
            {"test", "test"},
        },
        Ports: []string{"1080", "8888", "3128", "8080"},
        Options: TestOptions{
            ThreadLimit:    10,
            TimeoutSeconds: 3,
        },
    }

    // 初始化文件配置
    cfg.Files.Input = "ip_output2.txt"
    cfg.Files.Outputs = map[string]string{
        "3":      "original.txt",
        "30":     "delay.txt",
        "multi":  "duplicate.txt",
        "noauth": "noauth.txt",
    }

    // 初始化输出格式
    cfg.Options.OutputFormats = map[string]bool{
        "3":      false,
        "30":     false,
        "multi":  false,
        "noauth": false,
    }

    return cfg
}