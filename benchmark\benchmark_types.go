package main

import (
	"sync"
	"time"
)

// Config 存储全局配置信息
type Config struct {
	Credentials [][2]string `json:"credentials"`
	Ports       []string    `json:"ports"`
	Files       FileConfig
	Options     TestOptions
}

// FileConfig 存储文件相关配置
type FileConfig struct {
	Input   string
	Outputs map[string]string // 输出文件映射
}

// TestOptions 存储测试相关选项
type TestOptions struct {
	ThreadLimit    int
	TimeoutSeconds int
	OutputFormats  map[string]bool
}

// ProxyCandidate 代理候选者
type ProxyCandidate struct {
	IP     string
	Port   string
	State  ProxyState
	Result *ProxyInfo // 如果测试成功，存储结果
}

// ProxyState 代理测试状态
type ProxyState int

const (
	StateUntested    ProxyState = iota // 未测试
	StateNoAuth                        // 无认证成功
	StateAuthSuccess                   // 认证成功
	StateFailed                        // 测试失败
)

// ProxyInfo 存储代理信息
type ProxyInfo struct {
	IP          string
	Port        string
	User        string
	Password    string
	Latency     time.Duration
	Original    string
	TestResults map[string]bool // 记录各目标测试结果
	Quality     string          // "high", "medium", "low"
}

// TestTarget 测试目标配置
type TestTarget struct {
	Name    string
	Address string
	Timeout time.Duration
}

// BenchmarkMetrics 性能指标
type BenchmarkMetrics struct {
	ThreadCount      int           `json:"thread_count"`
	TotalTime        time.Duration `json:"total_time_ms"`
	TotalTests       int64         `json:"total_tests"`
	SuccessfulTests  int64         `json:"successful_tests"`
	FailedTests      int64         `json:"failed_tests"`
	TPS              float64       `json:"tests_per_second"`
	SuccessRate      float64       `json:"success_rate_percent"`
	AvgConnTime      time.Duration `json:"avg_connection_time_ms"`
	MinConnTime      time.Duration `json:"min_connection_time_ms"`
	MaxConnTime      time.Duration `json:"max_connection_time_ms"`
	CPUUsageStart    float64       `json:"cpu_usage_start_percent"`
	CPUUsageEnd      float64       `json:"cpu_usage_end_percent"`
	MemoryUsageStart uint64        `json:"memory_usage_start_mb"`
	MemoryUsageEnd   uint64        `json:"memory_usage_end_mb"`
	GoroutineCount   int           `json:"goroutine_count"`
}

// BenchmarkRunner 性能测试运行器
type BenchmarkRunner struct {
	config      *Config
	testProxies []ProxyCandidate
	results     []BenchmarkMetrics
	mu          sync.Mutex
}
