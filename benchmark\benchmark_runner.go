package main

import (
	"bufio"
	"fmt"
	"os"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"golang.org/x/net/proxy"
)

// NewBenchmarkRunner 创建性能测试运行器
func NewBenchmarkRunner(config *Config) *BenchmarkRunner {
	return &BenchmarkRunner{
		config:  config,
		results: make([]BenchmarkMetrics, 0),
	}
}

// RunBenchmarkSuite 运行完整性能测试套件
func (br *BenchmarkRunner) RunBenchmarkSuite() error {
	fmt.Println("🚀 启动SOCKS5代理性能测试套件")
	fmt.Println(strings.Repeat("=", 60))

	// 加载测试数据
	if err := br.loadTestProxies(); err != nil {
		return fmt.Errorf("加载测试代理失败: %v", err)
	}

	// 测试不同线程数配置
	threadCounts := []int{1, 5, 10, 20, 50, 100, 200}

	for i, threadCount := range threadCounts {
		fmt.Printf("\n📊 测试配置 %d/%d: ThreadLimit = %d\n", i+1, len(threadCounts), threadCount)
		fmt.Println(strings.Repeat("-", 40))

		metrics, err := br.runSingleBenchmark(threadCount)
		if err != nil {
			fmt.Printf("❌ 测试失败: %v\n", err)
			continue
		}

		br.results = append(br.results, *metrics)
		br.printMetrics(metrics)

		// 测试间隔，避免服务器压力过大
		if i < len(threadCounts)-1 {
			fmt.Printf("⏳ 等待5秒后进行下一轮测试...\n")
			time.Sleep(5 * time.Second)
		}
	}

	// 生成综合报告
	br.generateReport()
	return nil
}

// runSingleBenchmark 运行单次性能测试
func (br *BenchmarkRunner) runSingleBenchmark(threadCount int) (*BenchmarkMetrics, error) {
	metrics := &BenchmarkMetrics{
		ThreadCount: threadCount,
		MinConnTime: time.Hour, // 初始化为最大值
	}

	// 记录开始状态
	metrics.CPUUsageStart = br.getCPUUsage()
	metrics.MemoryUsageStart = br.getMemoryUsage()
	startTime := time.Now()

	// 性能监控
	var connTimes []time.Duration
	var connTimesMu sync.Mutex

	// 创建信号量控制并发
	semaphore := make(chan struct{}, threadCount)
	var wg sync.WaitGroup

	fmt.Printf("开始测试 %d 个代理，线程数: %d\n", len(br.testProxies), threadCount)

	// 执行测试
	for _, proxyCandidate := range br.testProxies {
		wg.Add(1)
		go func(ip, port string) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 测试单个代理
			connStart := time.Now()
			success := br.testSingleProxy(ip, port)
			connTime := time.Since(connStart)

			// 记录连接时间
			connTimesMu.Lock()
			connTimes = append(connTimes, connTime)
			if connTime < metrics.MinConnTime {
				metrics.MinConnTime = connTime
			}
			if connTime > metrics.MaxConnTime {
				metrics.MaxConnTime = connTime
			}
			connTimesMu.Unlock()

			// 更新统计
			if success {
				atomic.AddInt64(&metrics.SuccessfulTests, 1)
			} else {
				atomic.AddInt64(&metrics.FailedTests, 1)
			}
			atomic.AddInt64(&metrics.TotalTests, 1)

		}(proxyCandidate.IP, proxyCandidate.Port)
	}

	// 等待所有测试完成
	wg.Wait()

	// 计算最终指标
	metrics.TotalTime = time.Since(startTime)
	metrics.CPUUsageEnd = br.getCPUUsage()
	metrics.MemoryUsageEnd = br.getMemoryUsage()
	metrics.GoroutineCount = runtime.NumGoroutine()

	if metrics.TotalTime > 0 {
		metrics.TPS = float64(metrics.TotalTests) / metrics.TotalTime.Seconds()
	}

	if metrics.TotalTests > 0 {
		metrics.SuccessRate = float64(metrics.SuccessfulTests) / float64(metrics.TotalTests) * 100
	}

	// 计算平均连接时间
	if len(connTimes) > 0 {
		var totalConnTime time.Duration
		for _, ct := range connTimes {
			totalConnTime += ct
		}
		metrics.AvgConnTime = totalConnTime / time.Duration(len(connTimes))
	}

	return metrics, nil
}

// testSingleProxy 测试单个代理
func (br *BenchmarkRunner) testSingleProxy(ip, port string) bool {
	// 创建SOCKS5代理连接
	dialAddr := fmt.Sprintf("%s:%s", ip, port)

	// 先尝试无认证
	dialer, err := proxy.SOCKS5("tcp", dialAddr, nil, proxy.Direct)
	if err != nil {
		return false
	}

	// 设置超时
	// context 变量 ctx 未使用，已移除

	// 尝试连接到测试目标
	conn, err := dialer.Dial("tcp", "************:8080")
	if err != nil {
		return false
	}
	defer conn.Close()

	// 设置读写超时
	conn.SetDeadline(time.Now().Add(time.Duration(br.config.Options.TimeoutSeconds) * time.Second))

	// 发送测试数据
	testData := "GET / HTTP/1.1\r\nHost: test\r\n\r\n"
	_, err = conn.Write([]byte(testData))
	if err != nil {
		return false
	}

	// 尝试读取响应
	buffer := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(1 * time.Second))
	_, err = conn.Read(buffer)

	// 只要能建立连接就算成功，不要求必须有响应
	return true
}

// loadTestProxies 加载测试代理列表
func (br *BenchmarkRunner) loadTestProxies() error {
	// 尝试从输入文件加载
	if br.config.Files.Input != "" {
		if proxies, err := br.loadFromFile(br.config.Files.Input); err == nil {
			br.testProxies = proxies
			fmt.Printf("📋 从文件加载了 %d 个测试代理\n", len(br.testProxies))
			return nil
		}
	}

	// 使用默认测试数据
	testProxies := []ProxyCandidate{
		{IP: "127.0.0.1", Port: "1080"},
		{IP: "*************", Port: "1080"},
		{IP: "********", Port: "1080"},
		// 添加更多测试代理...
	}

	// 限制测试数量以确保测试可控
	maxTestCount := 50
	if len(testProxies) > maxTestCount {
		testProxies = testProxies[:maxTestCount]
	}

	br.testProxies = testProxies
	fmt.Printf("📋 使用默认测试代理 %d 个\n", len(br.testProxies))
	return nil
}

// loadFromFile 从文件加载代理列表
func (br *BenchmarkRunner) loadFromFile(filename string) ([]ProxyCandidate, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var proxies []ProxyCandidate
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			proxies = append(proxies, ProxyCandidate{
				IP:    parts[0],
				Port:  parts[1],
				State: StateUntested,
			})
		}
	}

	return proxies, scanner.Err()
}

// getCPUUsage 获取CPU使用率（简化版本）
func (br *BenchmarkRunner) getCPUUsage() float64 {
	// Windows下的CPU监控实现（简化版）
	return float64(runtime.NumGoroutine()) / float64(runtime.NumCPU()) * 10
}

// getMemoryUsage 获取内存使用量（MB）
func (br *BenchmarkRunner) getMemoryUsage() uint64 {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	return m.Alloc / 1024 / 1024 // 转换为MB
}
