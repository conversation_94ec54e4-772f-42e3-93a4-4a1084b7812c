package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"sync"
)

// ProxyTester 代理测试器
type ProxyTester struct {
	config     *Config
	limiter    *Thread
	seen       *sync.Map
	results    *TestResults
	resultLock sync.Mutex
}

// NewProxyTester 创建新的代理测试器
func NewProxyTester(config *Config) *ProxyTester {
	return &ProxyTester{
		config:  config,
		limiter: NewThread(config.Options.ThreadLimit),
		seen:    &sync.Map{},
		results: &TestResults{},
	}
}

// Run 运行代理测试 - 使用新的分阶段批量测试
func (pt *ProxyTester) Run() (*TestResults, error) {
	file, err := os.Open(pt.config.Files.Input)
	if err != nil {
		return nil, fmt.Errorf("无法打开输入文件: %v", err)
	}
	defer file.Close()

	// 创建批量测试协调器
	batchTester := NewBatchTestCoordinator(pt.config)

	// 读取所有代理并添加到候选列表
	reader := bufio.NewReader(file)
	for {
		line, isPrefix, err := reader.ReadLine()
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			return nil, err
		}

		fullLine := string(line)
		for isPrefix {
			line, isPrefix, err = reader.ReadLine()
			if err != nil {
				return nil, err
			}
			fullLine += string(line)
		}

		// 解析代理信息并添加到批量测试器
		parts := strings.Fields(fullLine)
		if len(parts) >= 2 {
			ip, port := parts[0], parts[1]
			batchTester.AddCandidate(ip, port)
		}
	}

	// 运行分阶段批量测试
	return batchTester.RunBatchTest()
}

// 注意：以下方法已被新的批量测试架构替代，保留用于向后兼容
// 如果确认新架构工作正常，可以删除这些方法

// startCleanup 启动清理任务（已废弃）
func (pt *ProxyTester) startCleanup() {
	// 在新的批量测试架构中不再需要
}

// processLine 处理单行代理信息（已废弃）
func (pt *ProxyTester) processLine(line string) {
	// 在新的批量测试架构中不再需要
}

// addResult 添加测试结果（已废弃）
func (pt *ProxyTester) addResult(proxy *ProxyInfo, category string) {
	// 在新的批量测试架构中不再需要
}
