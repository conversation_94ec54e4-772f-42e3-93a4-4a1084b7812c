package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"
)

// ProxyTester 代理测试器
type ProxyTester struct {
	config     *Config
	limiter    *Thread
	seen       *sync.Map
	results    *TestResults
	resultLock sync.Mutex
}

// NewProxyTester 创建新的代理测试器
func NewProxyTester(config *Config) *ProxyTester {
	return &ProxyTester{
		config:  config,
		limiter: NewThread(config.Options.ThreadLimit),
		seen:    &sync.Map{},
		results: &TestResults{},
	}
}

// Run 运行代理测试
func (pt *ProxyTester) Run() (*TestResults, error) {
	file, err := os.Open(pt.config.Files.Input)
	if err != nil {
		return nil, fmt.Errorf("无法打开输入文件: %v", err)
	}
	defer file.Close()

	reader := bufio.NewReader(file)
	pt.startCleanup()

	for {
		line, isPrefix, err := reader.ReadLine()
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			return nil, err
		}

		fullLine := string(line)
		for isPrefix {
			line, isPrefix, err = reader.ReadLine()
			if err != nil {
				return nil, err
			}
			fullLine += string(line)
		}

		pt.processLine(fullLine)
	}

	pt.limiter.Wait()
	return pt.results, nil
}

// startCleanup 启动清理任务
func (pt *ProxyTester) startCleanup() {
	cleanupTicker := time.NewTicker(5 * time.Minute)
	go func() {
		for range cleanupTicker.C {
			pt.seen.Range(func(key, value interface{}) bool {
				pt.seen.Delete(key)
				return true
			})
		}
	}()
}

// processLine 处理单行代理信息
func (pt *ProxyTester) processLine(line string) {
	parts := strings.Fields(line)
	if len(parts) < 2 {
		return
	}

	ip, port := parts[0], parts[1]
	/*
		portAllowed := false
		for _, allowedPort := range pt.config.Ports {
			if port == allowedPort {
				portAllowed = true
				break
			}
		}

			if !portAllowed {
				fmt.Printf("端口 %s 未定义，跳过\n", port)
				return
			}
	*/
	pt.limiter.Run(func(arg map[string]any) {
		ip := arg["ip"].(string)
		port := arg["port"].(string)
		pt.testProxy(ip, port)
	}, map[string]any{
		"ip":   ip,
		"port": port,
	})
}

// addResult 添加测试结果
func (pt *ProxyTester) addResult(proxy *ProxyInfo, category string) {
	pt.resultLock.Lock()
	defer pt.resultLock.Unlock()

	proxyKey := fmt.Sprintf("%s:%s", proxy.IP, proxy.Port)
	if _, exists := pt.seen.LoadOrStore(proxyKey, true); exists {
		return
	}

	switch category {
	case "normal":
		pt.results.Normal = append(pt.results.Normal, *proxy)
	case "multi":
		pt.results.Multi = append(pt.results.Multi, *proxy)
	case "noauth":
		pt.results.NoAuth = append(pt.results.NoAuth, *proxy)
	}
}
