import re

def parse_proxy_line(line):
    # 使用正则表达式匹配代理格式
    match = re.match(r'socks5://([^:]+):([^@]+)@([^:]+):(\d+)', line)
    if match:
        username = match.group(1)
        password = match.group(2)
        ip = match.group(3)
        port = match.group(4)
        return f"{ip} {port} {username} {password}"
    return None

def convert_proxies(input_file, output_file):
    with open(input_file, 'r') as file:
        lines = file.readlines()

    with open(output_file, 'w') as file:
        for line in lines:
            converted_line = parse_proxy_line(line.strip())
            if converted_line:
                file.write(converted_line + '\n')

# 调用函数，替换 'proxies.txt' 和 'converted_proxies.txt' 为你的文件名
convert_proxies('validated_proxies3.txt', 's5.txt')
