package main

import (
	"context"
	"fmt"
	"net"
	"time"

	"golang.org/x/net/proxy"
)

// testProxy 测试单个代理
func (pt *ProxyTester) testProxy(ip, port string) {
	// 1. 测试无认证 - 多目标验证
	if proxy, quality := pt.testMultiTargets(ip, port, "", ""); proxy != nil {
		pt.addResultWithQuality(proxy, "noauth", quality)
		return
	}

	// 2. 测试固定认证xxx:yyy
	if proxy, quality := pt.testMultiTargets(ip, port, "xxx", "yyy"); proxy != nil {
		pt.addResultWithQuality(proxy, "multi", quality)
		return
	}

	// 3. 并行测试认证库
	if proxy := pt.testCredentialsParallelMultiTarget(ip, port); proxy != nil {
		pt.addResultWithQuality(proxy, "normal", proxy.Quality)
	}
}

// testNoAuth 测试无认证代理
func (pt *ProxyTester) testNoAuth(ip, port string) bool {
	timeout := time.Duration(pt.config.Options.TimeoutSeconds) * time.Second
	dialAddr := fmt.Sprintf("%s:%s", ip, port)
	dialer, err := proxy.SOCKS5("tcp", dialAddr, nil, proxy.Direct)
	if err != nil {
		fmt.Printf("代理测试失败：%s：%v\n", dialAddr, err)
		return false
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	connChan := make(chan net.Conn, 1)
	errChan := make(chan error, 1)

	go func() {
		conn, err := dialer.Dial("tcp", "ipinfo.io:80")
		if err != nil {
			errChan <- err
			return
		}
		connChan <- conn
	}()

	select {
	case conn := <-connChan:
		if conn != nil {
			conn.Close()
			fmt.Printf("发现无认证代理：%s\n", dialAddr)
			return true
		}
		return false
	case <-errChan:
		fmt.Printf("代理测试失败：%s\n", dialAddr)
		return false
	case <-ctx.Done():
		fmt.Printf("代理超时：%s\n", dialAddr)
		return false
	}
}

// testWithAuth 测试带认证的代理
// 返回值：延迟时间，是否验证成功，是否超时
func (pt *ProxyTester) testWithAuth(ip, port, user, password string) (time.Duration, bool, bool) {
	timeout := time.Duration(pt.config.Options.TimeoutSeconds) * time.Second
	dialAddr := fmt.Sprintf("%s:%s", ip, port)
	auth := &proxy.Auth{
		User:     user,
		Password: password,
	}

	dialer, err := proxy.SOCKS5("tcp", dialAddr, auth, proxy.Direct)
	if err != nil {
		return 0, false, false
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	start := time.Now()
	connChan := make(chan net.Conn, 1)
	errChan := make(chan error, 1)

	go func() {
		conn, err := dialer.Dial("tcp", "ipinfo.io:80")
		if err != nil {
			errChan <- err
			return
		}
		connChan <- conn
	}()

	select {
	case conn := <-connChan:
		if conn == nil {
			return 0, false, false
		}
		latency := time.Since(start)
		conn.Close()
		fmt.Printf("发现有效代理：%s（用户：%s，密码：%s）\n", dialAddr, user, password)
		return latency, true, false
	case <-errChan:
		return 0, false, false
	case <-ctx.Done():
		fmt.Printf("代理超时：%s（用户：%s）\n", dialAddr, user)
		return 0, false, true // 最后一个参数表示是否超时
	}
}

// 并行认证测试 - 同时测试所有认证组合
func (pt *ProxyTester) testCredentialsParallel(ip, port string) (*ProxyInfo, bool) {
	credCount := len(pt.config.Credentials)
	resultChan := make(chan *ProxyInfo, credCount)
	doneChan := make(chan bool, credCount)

	// 同时启动所有认证测试
	for _, cred := range pt.config.Credentials {
		go func(user, pass string) {
			defer func() { doneChan <- true }()

			latency, valid, timeout := pt.testWithAuth(ip, port, user, pass)
			if valid {
				proxy := &ProxyInfo{
					IP: ip, Port: port, User: user, Password: pass,
					Latency: latency, Original: fmt.Sprintf("%s %s", ip, port),
				}
				resultChan <- proxy
			}
		}(cred[0], cred[1])
	}

	// 等待第一个成功结果或全部完成
	completed := 0
	timeout := time.After(time.Duration(pt.config.Options.TimeoutSeconds) * time.Second)

	for {
		select {
		case proxy := <-resultChan:
			return proxy, true // 找到有效代理，立即返回
		case <-doneChan:
			completed++
			if completed == credCount {
				return nil, false // 所有认证都失败
			}
		case <-timeout:
			return nil, true // 超时
		}
	}
}

// 多目标测试函数
func (pt *ProxyTester) testMultiTargets(ip, port, user, password string) (*ProxyInfo, string) {
	targets := []TestTarget{
		{"local", "localhost:8080", 1 * time.Second}, // 自建服务器
		{"ipinfo", "ipinfo.io:80", 3 * time.Second},  // 公共服务
	}

	proxy := &ProxyInfo{
		IP: ip, Port: port, User: user, Password: password,
		Original:    fmt.Sprintf("%s %s", ip, port),
		TestResults: make(map[string]bool),
	}

	passedCount := 0
	totalLatency := time.Duration(0)

	for _, target := range targets {
		latency, valid := pt.testSingleTarget(ip, port, user, password, target)
		proxy.TestResults[target.Name] = valid

		if valid {
			passedCount++
			totalLatency += latency
		}
	}

	// 计算平均延迟和质量等级
	if passedCount > 0 {
		proxy.Latency = totalLatency / time.Duration(passedCount)
	}

	switch passedCount {
	case len(targets):
		proxy.Quality = "high" // 所有目标都通过
	case 1:
		proxy.Quality = "medium" // 部分通过
	default:
		return nil, "failed" // 全部失败
	}

	return proxy, proxy.Quality
}

// 单目标测试
func (pt *ProxyTester) testSingleTarget(ip, port, user, password string, target TestTarget) (time.Duration, bool) {
	dialAddr := fmt.Sprintf("%s:%s", ip, port)
	var auth *proxy.Auth
	if user != "" {
		auth = &proxy.Auth{User: user, Password: password}
	}

	dialer, err := proxy.SOCKS5("tcp", dialAddr, auth, proxy.Direct)
	if err != nil {
		return 0, false
	}

	ctx, cancel := context.WithTimeout(context.Background(), target.Timeout)
	defer cancel()

	start := time.Now()
	conn, err := dialer.Dial("tcp", target.Address) // 使用目标地址
	if err != nil {
		return 0, false
	}
	defer conn.Close()

	return time.Since(start), true
}

// 扩展结果添加函数
func (pt *ProxyTester) addResultWithQuality(proxy *ProxyInfo, category, quality string) {
	pt.resultLock.Lock()
	defer pt.resultLock.Unlock()

	proxyKey := fmt.Sprintf("%s:%s", proxy.IP, proxy.Port)
	if _, exists := pt.seen.LoadOrStore(proxyKey, true); exists {
		return
	}

	// 根据质量分类存储
	switch category + "_" + quality {
	case "noauth_high":
		pt.results.NoAuthHigh = append(pt.results.NoAuthHigh, *proxy)
	case "noauth_medium":
		pt.results.NoAuthMedium = append(pt.results.NoAuthMedium, *proxy)
		// ... 其他分类
	}
}
