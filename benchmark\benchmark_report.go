package main

import (
    "encoding/json"
    "fmt"
    "os"
    "sort"
    "time"
)

// printMetrics 打印单次测试指标
func (br *BenchmarkRunner) printMetrics(m *BenchmarkMetrics) {
    fmt.Printf("⏱️  总耗时: %v\n", m.TotalTime)
    fmt.Printf("🎯 测试总数: %d (成功: %d, 失败: %d)\n", m.TotalTests, m.SuccessfulTests, m.FailedTests)
    fmt.Printf("📈 TPS: %.2f 次/秒\n", m.TPS)
    fmt.Printf("✅ 成功率: %.1f%%\n", m.SuccessRate)
    fmt.Printf("🔗 连接时间: 平均 %v, 最小 %v, 最大 %v\n", m.AvgConnTime, m.MinConnTime, m.MaxConnTime)
    fmt.Printf("💾 内存使用: %d MB → %d MB (增加 %d MB)\n", 
        m.MemoryUsageStart, m.MemoryUsageEnd, m.MemoryUsageEnd-m.MemoryUsageStart)
    fmt.Printf("🧵 协程数量: %d\n", m.GoroutineCount)
}

// generateReport 生成综合性能报告
func (br *BenchmarkRunner) generateReport() {
    fmt.Println("\n" + "=" * 80)
    fmt.Println("📊 SOCKS5代理测试性能分析报告")
    fmt.Println("=" * 80)
    
    // 1. 性能对比表格
    br.printPerformanceTable()
    
    // 2. 最佳配置推荐
    br.recommendOptimalConfig()
    
    // 3. 瓶颈分析
    br.analyzeBottlenecks()
    
    // 4. 保存详细报告
    br.saveDetailedReport()
}

// printPerformanceTable 打印性能对比表格
func (br *BenchmarkRunner) printPerformanceTable() {
    fmt.Println("\n📋 性能对比表格:")
    fmt.Println("-" * 80)
    fmt.Printf("%-8s %-10s %-8s %-10s %-12s %-10s %-8s\n", 
        "线程数", "总耗时(s)", "TPS", "成功率(%)", "平均连接(ms)", "内存(MB)", "协程数")
    fmt.Println("-" * 80)
    
    for _, m := range br.results {
        fmt.Printf("%-8d %-10.1f %-8.1f %-10.1f %-12d %-10d %-8d\n",
            m.ThreadCount,
            m.TotalTime.Seconds(),
            m.TPS,
            m.SuccessRate,
            m.AvgConnTime.Milliseconds(),
            m.MemoryUsageEnd,
            m.GoroutineCount)
    }
}

// recommendOptimalConfig 推荐最佳配置
func (br *BenchmarkRunner) recommendOptimalConfig() {
    fmt.Println("\n🎯 最佳配置推荐:")
    fmt.Println("-" * 40)
    
    if len(br.results) == 0 {
        fmt.Println("❌ 无测试数据")
        return
    }
    
    // 按TPS排序找出最佳性能
    sortedByTPS := make([]BenchmarkMetrics, len(br.results))
    copy(sortedByTPS, br.results)
    sort.Slice(sortedByTPS, func(i, j int) bool {
        return sortedByTPS[i].TPS > sortedByTPS[j].TPS
    })
    
    best := sortedByTPS[0]
    fmt.Printf("🏆 最高TPS配置: %d线程 (%.1f TPS)\n", best.ThreadCount, best.TPS)
    
    // 找出性价比最高的配置（TPS/线程数比值最高）
    var bestEfficiency BenchmarkMetrics
    maxEfficiency := 0.0
    
    for _, m := range br.results {
        efficiency := m.TPS / float64(m.ThreadCount)
        if efficiency > maxEfficiency {
            maxEfficiency = efficiency
            bestEfficiency = m
        }
    }
    
    fmt.Printf("⚡ 最高效率配置: %d线程 (效率: %.2f TPS/线程)\n", 
        bestEfficiency.ThreadCount, maxEfficiency)
    
    // 分析边际效益
    br.analyzeMarginalBenefit()
}

// analyzeMarginalBenefit 分析边际效益
func (br *BenchmarkRunner) analyzeMarginalBenefit() {
    fmt.Println("\n📈 边际效益分析:")
    
    sort.Slice(br.results, func(i, j int) bool {
        return br.results[i].ThreadCount < br.results[j].ThreadCount
    })
    
    for i := 1; i < len(br.results); i++ {
        prev := br.results[i-1]
        curr := br.results[i]
        
        threadIncrease := curr.ThreadCount - prev.ThreadCount
        tpsIncrease := curr.TPS - prev.TPS
        marginalBenefit := tpsIncrease / float64(threadIncrease)
        
        fmt.Printf("  %d→%d线程: TPS提升 %.1f (边际效益: %.2f TPS/线程)\n",
            prev.ThreadCount, curr.ThreadCount, tpsIncrease, marginalBenefit)
        
        // 标记边际效益显著下降的点
        if marginalBenefit < 0.1 {
            fmt.Printf("    ⚠️  边际效益显著下降，建议不超过 %d 线程\n", prev.ThreadCount)
        }
    }
}

// analyzeBottlenecks 分析性能瓶颈
func (br *BenchmarkRunner) analyzeBottlenecks() {
    fmt.Println("\n🔍 性能瓶颈分析:")
    fmt.Println("-" * 40)
    
    if len(br.results) < 2 {
        fmt.Println("❌ 数据不足，无法分析")
        return
    }
    
    // 分析TPS增长趋势
    lowThread := br.results[0]
    highThread := br.results[len(br.results)-1]
    
    tpsGrowthRatio := highThread.TPS / lowThread.TPS
    threadGrowthRatio := float64(highThread.ThreadCount) / float64(lowThread.ThreadCount)
    
    fmt.Printf("📊 线程数增长 %.1fx，TPS增长 %.1fx\n", threadGrowthRatio, tpsGrowthRatio)
    
    if tpsGrowthRatio < threadGrowthRatio * 0.3 {
        fmt.Println("🚨 主要瓶颈: 网络I/O")
        fmt.Println("   - 网络延迟是主要限制因素")
        fmt.Println("   - 建议优化: 减少连接超时时间、使用连接池")
    } else if tpsGrowthRatio < threadGrowthRatio * 0.6 {
        fmt.Println("⚠️  主要瓶颈: 服务器处理能力")
        fmt.Println("   - 目标服务器并发处理能力有限")
        fmt.Println("   - 建议优化: 增加测试目标、负载均衡")
    } else {
        fmt.Println("✅ 系统扩展性良好")
        fmt.Println("   - 可以适当增加线程数")
    }
    
    // 分析连接时间趋势
    avgConnTime := time.Duration(0)
    for _, m := range br.results {
        avgConnTime += m.AvgConnTime
    }
    avgConnTime /= time.Duration(len(br.results))
    
    fmt.Printf("🔗 平均连接建立时间: %v\n", avgConnTime)
    if avgConnTime > 1*time.Second {
        fmt.Println("   ⚠️  连接时间较长，网络I/O是主要瓶颈")
    }
}

// saveDetailedReport 保存详细报告
func (br *BenchmarkRunner) saveDetailedReport() {
    timestamp := time.Now().Format("20060102_150405")
    filename := fmt.Sprintf("benchmark_report_%s.json", timestamp)
    
    report := map[string]interface{}{
        "timestamp": time.Now(),
        "summary": map[string]interface{}{
            "total_configurations": len(br.results),
            "test_duration": time.Since(time.Now()).String(),
        },
        "results": br.results,
    }
    
    data, err := json.MarshalIndent(report, "", "  ")
    if err != nil {
        fmt.Printf("❌ 生成报告失败: %v\n", err)
        return
    }
    
    if err := os.WriteFile(filename, data, 0644); err != nil {
        fmt.Printf("❌ 保存报告失败: %v\n", err)
        return
    }
    
    fmt.Printf("\n💾 详细报告已保存: %s\n", filename)
}