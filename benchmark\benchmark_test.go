package main

import (
    "context"
    "fmt"
    "runtime"
    "sync"
    "sync/atomic"
    "time"
)

// BenchmarkMetrics 性能指标
type BenchmarkMetrics struct {
    ThreadCount        int           `json:"thread_count"`
    TotalTime         time.Duration `json:"total_time_ms"`
    TotalTests        int64         `json:"total_tests"`
    SuccessfulTests   int64         `json:"successful_tests"`
    FailedTests       int64         `json:"failed_tests"`
    TPS               float64       `json:"tests_per_second"`
    SuccessRate       float64       `json:"success_rate_percent"`
    AvgConnTime       time.Duration `json:"avg_connection_time_ms"`
    MinConnTime       time.Duration `json:"min_connection_time_ms"`
    MaxConnTime       time.Duration `json:"max_connection_time_ms"`
    CPUUsageStart     float64       `json:"cpu_usage_start_percent"`
    CPUUsageEnd       float64       `json:"cpu_usage_end_percent"`
    MemoryUsageStart  uint64        `json:"memory_usage_start_mb"`
    MemoryUsageEnd    uint64        `json:"memory_usage_end_mb"`
    GoroutineCount    int           `json:"goroutine_count"`
}

// BenchmarkRunner 性能测试运行器
type BenchmarkRunner struct {
    config       *Config
    testProxies  []ProxyCandidate
    results      []BenchmarkMetrics
    mu           sync.Mutex
}

// NewBenchmarkRunner 创建性能测试运行器
func NewBenchmarkRunner(config *Config) *BenchmarkRunner {
    return &BenchmarkRunner{
        config:  config,
        results: make([]BenchmarkMetrics, 0),
    }
}

// RunBenchmarkSuite 运行完整性能测试套件
func (br *BenchmarkRunner) RunBenchmarkSuite() error {
    fmt.Println("🚀 启动SOCKS5代理性能测试套件")
    fmt.Println("=" * 60)
    
    // 加载测试数据
    if err := br.loadTestProxies(); err != nil {
        return fmt.Errorf("加载测试代理失败: %v", err)
    }
    
    // 测试不同线程数配置
    threadCounts := []int{1, 5, 10, 20, 50, 100, 200}
    
    for i, threadCount := range threadCounts {
        fmt.Printf("\n📊 测试配置 %d/%d: ThreadLimit = %d\n", i+1, len(threadCounts), threadCount)
        fmt.Println("-" * 40)
        
        metrics, err := br.runSingleBenchmark(threadCount)
        if err != nil {
            fmt.Printf("❌ 测试失败: %v\n", err)
            continue
        }
        
        br.results = append(br.results, *metrics)
        br.printMetrics(metrics)
        
        // 测试间隔，避免服务器压力过大
        if i < len(threadCounts)-1 {
            fmt.Printf("⏳ 等待5秒后进行下一轮测试...\n")
            time.Sleep(5 * time.Second)
        }
    }
    
    // 生成综合报告
    br.generateReport()
    return nil
}

// runSingleBenchmark 运行单次性能测试
func (br *BenchmarkRunner) runSingleBenchmark(threadCount int) (*BenchmarkMetrics, error) {
    metrics := &BenchmarkMetrics{
        ThreadCount: threadCount,
        MinConnTime: time.Hour, // 初始化为最大值
    }
    
    // 记录开始状态
    metrics.CPUUsageStart = br.getCPUUsage()
    metrics.MemoryUsageStart = br.getMemoryUsage()
    startTime := time.Now()
    
    // 创建测试器
    testConfig := *br.config
    testConfig.Options.ThreadLimit = threadCount
    tester := NewProxyTester(&testConfig)
    
    // 性能监控
    var connTimes []time.Duration
    var connTimesMu sync.Mutex
    
    // 重写testSingleTarget以收集连接时间
    originalTestFunc := tester.testSingleTarget
    tester.testSingleTarget = func(ip, port, user, password string, target TestTarget) (time.Duration, bool) {
        connStart := time.Now()
        latency, success := originalTestFunc(ip, port, user, password, target)
        connTime := time.Since(connStart)
        
        connTimesMu.Lock()
        connTimes = append(connTimes, connTime)
        if connTime < metrics.MinConnTime {
            metrics.MinConnTime = connTime
        }
        if connTime > metrics.MaxConnTime {
            metrics.MaxConnTime = connTime
        }
        connTimesMu.Unlock()
        
        if success {
            atomic.AddInt64(&metrics.SuccessfulTests, 1)
        } else {
            atomic.AddInt64(&metrics.FailedTests, 1)
        }
        atomic.AddInt64(&metrics.TotalTests, 1)
        
        return latency, success
    }
    
    // 执行测试
    fmt.Printf("开始测试 %d 个代理，线程数: %d\n", len(br.testProxies), threadCount)
    
    for _, proxy := range br.testProxies {
        tester.limiter.Run(func(args map[string]any) {
            ip := args["ip"].(string)
            port := args["port"].(string)
            tester.testProxy(ip, port)
        }, map[string]any{
            "ip":   proxy.IP,
            "port": proxy.Port,
        })
    }
    
    // 等待所有测试完成
    tester.limiter.Wait()
    
    // 计算最终指标
    metrics.TotalTime = time.Since(startTime)
    metrics.CPUUsageEnd = br.getCPUUsage()
    metrics.MemoryUsageEnd = br.getMemoryUsage()
    metrics.GoroutineCount = runtime.NumGoroutine()
    
    if metrics.TotalTime > 0 {
        metrics.TPS = float64(metrics.TotalTests) / metrics.TotalTime.Seconds()
    }
    
    if metrics.TotalTests > 0 {
        metrics.SuccessRate = float64(metrics.SuccessfulTests) / float64(metrics.TotalTests) * 100
    }
    
    // 计算平均连接时间
    if len(connTimes) > 0 {
        var totalConnTime time.Duration
        for _, ct := range connTimes {
            totalConnTime += ct
        }
        metrics.AvgConnTime = totalConnTime / time.Duration(len(connTimes))
    }
    
    return metrics, nil
}