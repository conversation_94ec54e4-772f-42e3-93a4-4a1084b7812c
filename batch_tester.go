package main

import (
	"fmt"
	"sync"
	"time"

	"golang.org/x/net/proxy"
)

// NewBatchTestCoordinator 创建新的批量测试协调器
func NewBatchTestCoordinator(config *Config) *BatchTestCoordinator {
	return &BatchTestCoordinator{
		candidates: make(map[string]*ProxyCandidate),
		config:     config,
		limiter:    NewThread(config.Options.ThreadLimit),
		results:    &TestResults{},
	}
}

// AddCandidate 添加代理候选者
func (btc *BatchTestCoordinator) AddCandidate(ip, port string) {
	key := fmt.Sprintf("%s:%s", ip, port)
	btc.candidates[key] = &ProxyCandidate{
		IP:    ip,
		Port:  port,
		State: StateUntested,
	}
}

// RunBatchTest 运行分阶段批量测试
func (btc *BatchTestCoordinator) RunBatchTest() (*TestResults, error) {
	totalCandidates := len(btc.candidates)
	fmt.Printf("========================================\n")
	fmt.Printf("开始分阶段批量测试 (总计 %d 个代理)\n", totalCandidates)
	fmt.Printf("========================================\n")

	// 阶段1: 无认证测试
	fmt.Printf("\n🔍 阶段1: 批量无认证测试 (测试 %d 个代理)\n", totalCandidates)
	fmt.Printf("----------------------------------------\n")
	btc.runNoAuthStage()

	// 阶段2: 固定认证测试
	remainingCount := btc.countByState(StateUntested)
	fmt.Printf("\n🔐 阶段2: 批量固定认证测试 (测试剩余 %d 个代理)\n", remainingCount)
	fmt.Printf("----------------------------------------\n")
	btc.runFixedAuthStage()

	// 阶段3: 认证库测试
	remainingCount = btc.countByState(StateUntested)
	fmt.Printf("\n🗝️  阶段3: 认证库批量测试 (测试剩余 %d 个代理)\n", remainingCount)
	fmt.Printf("----------------------------------------\n")
	btc.runCredentialPoolStage()

	// 最终统计
	fmt.Printf("\n========================================\n")
	fmt.Printf("所有阶段测试完成 - 最终统计:\n")
	fmt.Printf("✓ 无认证成功: %d 个\n", btc.countByState(StateNoAuth))
	fmt.Printf("✓ 认证成功: %d 个\n", btc.countByState(StateAuthSuccess))
	fmt.Printf("✗ 测试失败: %d 个\n", totalCandidates-btc.countByState(StateNoAuth)-btc.countByState(StateAuthSuccess))
	fmt.Printf("========================================\n")

	return btc.results, nil
}

// runNoAuthStage 运行无认证测试阶段
func (btc *BatchTestCoordinator) runNoAuthStage() {
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 创建工作通道，提高并发效率
	workChan := make(chan *ProxyCandidate, len(btc.candidates))

	// 将待测试的代理放入通道
	for _, candidate := range btc.candidates {
		if candidate.State == StateUntested {
			workChan <- candidate
		}
	}
	close(workChan)

	// 启动工作协程
	workerCount := btc.config.Options.ThreadLimit
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for candidate := range workChan {
				ip := candidate.IP
				port := candidate.Port
				key := fmt.Sprintf("%s:%s", ip, port)

				// 测试无认证连接
				if proxy, quality := btc.testMultiTargets(ip, port, "", ""); proxy != nil {
					mu.Lock()
					candidate := btc.candidates[key]
					candidate.State = StateNoAuth
					btc.addAnonymousResult(proxy, quality)
					mu.Unlock()

					fmt.Printf("✓ 无认证成功: %s:%s (质量: %s)\n", ip, port, quality)
				}
			}
		}()
	}

	wg.Wait()
}

// runFixedAuthStage 运行固定认证测试阶段
func (btc *BatchTestCoordinator) runFixedAuthStage() {
	// 固定认证组合
	fixedCreds := [][2]string{
		{"admin", "admin"},
		{"root", "root"},
		{"123456", "123456"},
		{"socks5", "socks5"},
	}

	for _, cred := range fixedCreds {
		user, password := cred[0], cred[1]
		fmt.Printf("🔑 测试固定认证: %s/%s\n", user, password)
		btc.runSingleCredentialTest(user, password)
	}
}

// runCredentialPoolStage 运行认证库测试阶段
func (btc *BatchTestCoordinator) runCredentialPoolStage() {
	for _, cred := range btc.config.Credentials {
		user, password := cred[0], cred[1]
		btc.runSingleCredentialTest(user, password)
	}
}

// runSingleCredentialTest 运行单一认证测试
func (btc *BatchTestCoordinator) runSingleCredentialTest(user, password string) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	successCount := 0

	// 创建工作通道
	workChan := make(chan *ProxyCandidate, len(btc.candidates))

	// 将未测试成功的代理放入通道
	for _, candidate := range btc.candidates {
		if candidate.State == StateUntested {
			workChan <- candidate
		}
	}
	close(workChan)

	// 启动工作协程
	workerCount := btc.config.Options.ThreadLimit
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for candidate := range workChan {
				ip := candidate.IP
				port := candidate.Port
				key := fmt.Sprintf("%s:%s", ip, port)

				// 测试认证连接
				if proxy, quality := btc.testMultiTargets(ip, port, user, password); proxy != nil {
					mu.Lock()
					candidate := btc.candidates[key]
					candidate.State = StateAuthSuccess
					btc.addAuthResult(proxy, quality)
					successCount++
					mu.Unlock()

					fmt.Printf("✓ 认证成功: %s:%s (%s/%s) (质量: %s)\n", ip, port, user, password, quality)
				}
			}
		}()
	}

	wg.Wait()

	if successCount > 0 {
		fmt.Printf("  → 本轮认证成功: %d 个\n", successCount)
	}
}

// countByState 统计指定状态的代理数量
func (btc *BatchTestCoordinator) countByState(state ProxyState) int {
	count := 0
	for _, candidate := range btc.candidates {
		if candidate.State == state {
			count++
		}
	}
	return count
}

// addAnonymousResult 添加无认证结果
func (btc *BatchTestCoordinator) addAnonymousResult(proxy *ProxyInfo, quality string) {
	btc.results.NoAuth = append(btc.results.NoAuth, *proxy)
	btc.results.Normal = append(btc.results.Normal, *proxy)
}

// addAuthResult 添加认证结果
func (btc *BatchTestCoordinator) addAuthResult(proxy *ProxyInfo, quality string) {
	btc.results.Multi = append(btc.results.Multi, *proxy)
	btc.results.Normal = append(btc.results.Normal, *proxy)
}

// testMultiTargets 测试多个目标
func (btc *BatchTestCoordinator) testMultiTargets(ip, port, user, password string) (*ProxyInfo, string) {
	// 测试目标列表
	targets := []TestTarget{
		{Name: "test1", Address: "************:8080", Timeout: 3 * time.Second},
		{Name: "test2", Address: "www.baidu.com:80", Timeout: 5 * time.Second},
	}

	var bestLatency time.Duration = time.Hour
	var hasSuccess bool

	for _, target := range targets {
		if latency, success := btc.testSingleTarget(ip, port, user, password, target); success {
			hasSuccess = true
			if latency < bestLatency {
				bestLatency = latency
			}
		}
	}

	if !hasSuccess {
		return nil, ""
	}

	// 构造代理信息
	proxy := &ProxyInfo{
		IP:       ip,
		Port:     port,
		User:     user,
		Password: password,
		Latency:  bestLatency,
	}

	// 根据延迟判断质量
	quality := "good"
	if bestLatency > 2*time.Second {
		quality = "slow"
	} else if bestLatency < 500*time.Millisecond {
		quality = "excellent"
	}

	return proxy, quality
}

// testSingleTarget 测试单个目标
func (btc *BatchTestCoordinator) testSingleTarget(ip, port, user, password string, target TestTarget) (time.Duration, bool) {
	dialAddr := fmt.Sprintf("%s:%s", ip, port)

	// 创建认证信息
	var auth *proxy.Auth
	if user != "" {
		auth = &proxy.Auth{
			User:     user,
			Password: password,
		}
	}

	// 创建SOCKS5代理连接
	dialer, err := proxy.SOCKS5("tcp", dialAddr, auth, proxy.Direct)
	if err != nil {
		return 0, false
	}

	// 设置超时（不使用 context，直接在连接上设置超时）

	// 测试连接
	start := time.Now()
	conn, err := dialer.Dial("tcp", target.Address)
	if err != nil {
		return 0, false
	}
	defer conn.Close()

	latency := time.Since(start)

	// 设置读写超时
	conn.SetDeadline(time.Now().Add(target.Timeout))

	// 发送测试数据
	testData := "GET / HTTP/1.1\r\nHost: test\r\n\r\n"
	_, err = conn.Write([]byte(testData))
	if err != nil {
		return latency, true // 连接成功但写入失败，仍算作成功
	}

	// 尝试读取响应
	buffer := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(1 * time.Second))
	_, err = conn.Read(buffer)

	// 只要能建立连接就算成功，不要求必须有响应
	return latency, true
}
