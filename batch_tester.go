package main

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"golang.org/x/net/proxy"
)

// NewBatchTestCoordinator 创建新的批量测试协调器
func NewBatchTestCoordinator(config *Config) *BatchTestCoordinator {
	return &BatchTestCoordinator{
		candidates: make(map[string]*ProxyCandidate),
		config:     config,
		limiter:    NewThread(config.Options.ThreadLimit),
		results:    &TestResults{},
	}
}

// AddCandidate 添加代理候选者
func (btc *BatchTestCoordinator) AddCandidate(ip, port string) {
	key := fmt.Sprintf("%s:%s", ip, port)
	btc.candidates[key] = &ProxyCandidate{
		IP:    ip,
		Port:  port,
		State: StateUntested,
	}
}

// RunBatchTest 运行分阶段批量测试
func (btc *BatchTestCoordinator) RunBatchTest() (*TestResults, error) {
	totalCandidates := len(btc.candidates)
	fmt.Printf("========================================\n")
	fmt.Printf("开始分阶段批量测试 (总计 %d 个代理)\n", totalCandidates)
	fmt.Printf("========================================\n")

	// 阶段1: 无认证测试
	fmt.Printf("\n🔍 阶段1: 批量无认证测试 (测试 %d 个代理)\n", totalCandidates)
	fmt.Printf("----------------------------------------\n")
	btc.runNoAuthStage()

	// 阶段2: 固定认证测试
	remainingCount := btc.countByState(StateUntested)
	fmt.Printf("\n🔐 阶段2: 批量固定认证测试 (测试剩余 %d 个代理)\n", remainingCount)
	fmt.Printf("----------------------------------------\n")
	btc.runFixedAuthStage()

	// 阶段3: 认证库测试
	remainingCount = btc.countByState(StateUntested)
	fmt.Printf("\n🗝️  阶段3: 认证库批量测试 (测试剩余 %d 个代理)\n", remainingCount)
	fmt.Printf("----------------------------------------\n")
	btc.runCredentialPoolStage()

	// 最终统计
	fmt.Printf("\n========================================\n")
	fmt.Printf("所有阶段测试完成 - 最终统计:\n")
	fmt.Printf("✓ 无认证成功: %d 个\n", btc.countByState(StateNoAuth))
	fmt.Printf("✓ 认证成功: %d 个\n", btc.countByState(StateAuthSuccess))
	fmt.Printf("✗ 测试失败: %d 个\n", totalCandidates-btc.countByState(StateNoAuth)-btc.countByState(StateAuthSuccess))
	fmt.Printf("========================================\n")

	return btc.results, nil
}

// runNoAuthStage 运行无认证测试阶段
func (btc *BatchTestCoordinator) runNoAuthStage() {
	var wg sync.WaitGroup
	var mu sync.Mutex

	for key, candidate := range btc.candidates {
		if candidate.State != StateUntested {
			continue
		}

		wg.Add(1)
		btc.limiter.Run(func(args map[string]any) {
			defer wg.Done()

			ip := args["ip"].(string)
			port := args["port"].(string)
			key := args["key"].(string)

			// 测试无认证连接
			if proxy, quality := btc.testMultiTargets(ip, port, "", ""); proxy != nil {
				mu.Lock()
				candidate := btc.candidates[key]
				candidate.State = StateNoAuth
				candidate.Result = proxy

				// 根据质量分类存储匿名代理
				if quality == "high" || quality == "medium" {
					btc.addAnonymousResult(proxy, quality)
					fmt.Printf("✓ 发现无认证代理: %s:%s (质量: %s)\n", ip, port, quality)
				} else {
					fmt.Printf("✗ 无认证代理质量不符合要求: %s:%s (质量: %s)\n", ip, port, quality)
				}
				mu.Unlock()
			} else {
				fmt.Printf("✗ 无认证测试失败: %s:%s\n", ip, port)
			}
		}, map[string]any{
			"ip":   candidate.IP,
			"port": candidate.Port,
			"key":  key,
		})
	}

	wg.Wait()
	successCount := btc.countByState(StateNoAuth)
	fmt.Printf("📊 无认证测试阶段完成 - 成功: %d 个，失败: %d 个\n",
		successCount, len(btc.candidates)-successCount)
}

// runFixedAuthStage 运行固定认证测试阶段
func (btc *BatchTestCoordinator) runFixedAuthStage() {
	var wg sync.WaitGroup
	var mu sync.Mutex

	for key, candidate := range btc.candidates {
		// 只测试未成功的代理
		if candidate.State != StateUntested {
			continue
		}

		wg.Add(1)
		btc.limiter.Run(func(args map[string]any) {
			defer wg.Done()

			ip := args["ip"].(string)
			port := args["port"].(string)
			key := args["key"].(string)

			// 测试固定认证 xxx:yyy
			if proxy, quality := btc.testMultiTargets(ip, port, "xxx", "yyy"); proxy != nil {
				mu.Lock()
				candidate := btc.candidates[key]
				candidate.State = StateAuthSuccess
				candidate.Result = proxy

				// 根据质量分类存储
				if quality == "high" || quality == "medium" {
					btc.addAuthResult(proxy, quality)
					fmt.Printf("✓ 发现固定认证代理: xxx:yyy@%s:%s (质量: %s)\n", ip, port, quality)
				} else {
					fmt.Printf("✗ 固定认证代理质量不符合要求: xxx:yyy@%s:%s (质量: %s)\n", ip, port, quality)
				}
				mu.Unlock()
			} else {
				fmt.Printf("✗ 固定认证测试失败: xxx:yyy@%s:%s\n", ip, port)
			}
		}, map[string]any{
			"ip":   candidate.IP,
			"port": candidate.Port,
			"key":  key,
		})
	}

	wg.Wait()
	fmt.Printf("固定认证测试阶段完成，成功: %d个\n", btc.countByState(StateAuthSuccess))
}

// runCredentialPoolStage 运行认证库测试阶段
func (btc *BatchTestCoordinator) runCredentialPoolStage() {
	// 按认证凭据逐个测试所有剩余代理
	for _, cred := range btc.config.Credentials {
		fmt.Printf("使用认证 %s:%s 测试剩余代理\n", cred[0], cred[1])
		btc.runSingleCredentialTest(cred[0], cred[1])
	}
}

// runSingleCredentialTest 使用单个认证凭据测试所有剩余代理
func (btc *BatchTestCoordinator) runSingleCredentialTest(user, password string) {
	var wg sync.WaitGroup
	var mu sync.Mutex

	for key, candidate := range btc.candidates {
		// 只测试未成功的代理
		if candidate.State != StateUntested {
			continue
		}

		wg.Add(1)
		btc.limiter.Run(func(args map[string]any) {
			defer wg.Done()

			ip := args["ip"].(string)
			port := args["port"].(string)
			key := args["key"].(string)
			user := args["user"].(string)
			password := args["password"].(string)

			if proxy, quality := btc.testMultiTargets(ip, port, user, password); proxy != nil {
				mu.Lock()
				candidate := btc.candidates[key]
				candidate.State = StateAuthSuccess
				candidate.Result = proxy

				// 根据质量分类存储
				if quality == "high" || quality == "medium" {
					btc.addAuthResult(proxy, quality)
					fmt.Printf("✓ 发现认证代理: %s:%s@%s:%s (质量: %s)\n", user, password, ip, port, quality)
				} else {
					fmt.Printf("✗ 认证代理质量不符合要求: %s:%s@%s:%s (质量: %s)\n", user, password, ip, port, quality)
				}
				mu.Unlock()
			} else {
				fmt.Printf("✗ 认证测试失败: %s:%s@%s:%s\n", user, password, ip, port)
			}
		}, map[string]any{
			"ip":       candidate.IP,
			"port":     candidate.Port,
			"key":      key,
			"user":     user,
			"password": password,
		})
	}

	wg.Wait()
}

// 辅助方法
func (btc *BatchTestCoordinator) countByState(state ProxyState) int {
	count := 0
	for _, candidate := range btc.candidates {
		if candidate.State == state {
			count++
		}
	}
	return count
}

// addAnonymousResult 添加匿名代理结果
func (btc *BatchTestCoordinator) addAnonymousResult(proxy *ProxyInfo, quality string) {
	if quality == "high" {
		btc.results.NoAuthHigh = append(btc.results.NoAuthHigh, *proxy)
	} else if quality == "medium" {
		btc.results.NoAuthMedium = append(btc.results.NoAuthMedium, *proxy)
	}
	btc.results.NoAuth = append(btc.results.NoAuth, *proxy)
}

// addAuthResult 添加认证代理结果
func (btc *BatchTestCoordinator) addAuthResult(proxy *ProxyInfo, quality string) {
	if quality == "high" {
		btc.results.Normal = append(btc.results.Normal, *proxy)
	} else if quality == "medium" {
		btc.results.Multi = append(btc.results.Multi, *proxy)
	}
}

// testMultiTargets 多目标测试方法（从proxy.go复制并适配）
func (btc *BatchTestCoordinator) testMultiTargets(ip, port, user, password string) (*ProxyInfo, string) {
	targets := []TestTarget{
		{"local", "************:8080", 1 * time.Second}, // 自建服务器
		{"ipinfo", "ipinfo.io:80", 3 * time.Second},     // 公共服务
	}

	proxy := &ProxyInfo{
		IP: ip, Port: port, User: user, Password: password,
		Original:    fmt.Sprintf("%s %s", ip, port),
		TestResults: make(map[string]bool),
	}

	passedCount := 0
	totalLatency := time.Duration(0)

	for _, target := range targets {
		latency, valid := btc.testSingleTarget(ip, port, user, password, target)
		proxy.TestResults[target.Name] = valid

		if valid {
			passedCount++
			totalLatency += latency
		}
	}

	// 计算平均延迟和质量等级
	if passedCount > 0 {
		proxy.Latency = totalLatency / time.Duration(passedCount)
	}

	switch passedCount {
	case len(targets):
		proxy.Quality = "high" // 所有目标都通过
	case 1:
		proxy.Quality = "medium" // 部分通过
	default:
		return nil, "failed" // 全部失败
	}

	return proxy, proxy.Quality
}

// testSingleTarget 单目标测试方法（从proxy.go复制并适配）
func (btc *BatchTestCoordinator) testSingleTarget(ip, port, user, password string, target TestTarget) (time.Duration, bool) {
	dialAddr := fmt.Sprintf("%s:%s", ip, port)
	var auth *proxy.Auth
	if user != "" {
		auth = &proxy.Auth{User: user, Password: password}
	}

	dialer, err := proxy.SOCKS5("tcp", dialAddr, auth, proxy.Direct)
	if err != nil {
		return 0, false
	}

	ctx, cancel := context.WithTimeout(context.Background(), target.Timeout)
	defer cancel()

	start := time.Now()

	// 使用context进行超时控制
	connChan := make(chan net.Conn, 1)
	errChan := make(chan error, 1)

	go func() {
		conn, err := dialer.Dial("tcp", target.Address)
		if err != nil {
			errChan <- err
			return
		}
		connChan <- conn
	}()

	select {
	case conn := <-connChan:
		defer conn.Close()
		return time.Since(start), true
	case <-errChan:
		return 0, false
	case <-ctx.Done():
		return 0, false // 超时
	}
}
