package main

import "time"

// ProxyInfo 存储代理信息
type ProxyInfo struct {
	IP          string
	Port        string
	User        string
	Password    string
	Latency     time.Duration
	Original    string
	TestResults map[string]bool // 记录各目标测试结果
	Quality     string          // "high", "medium", "low"
}

// TestResults 存储测试结果 - 只记录三种类型的代理
type TestResults struct {
	Normal       []ProxyInfo
	Multi        []ProxyInfo
	NoAuth       []ProxyInfo
	NoAuthHigh   []ProxyInfo // 高质量无认证代理
	NoAuthMedium []ProxyInfo // 中等质量无认证代理
}

// 测试目标配置
type TestTarget struct {
	Name    string
	Address string
	Timeout time.Duration
}

// ProxyState 代理测试状态
type ProxyState int

const (
	StateUntested    ProxyState = iota // 未测试
	StateNoAuth                        // 无认证成功
	StateAuthSuccess                   // 认证成功
	StateFailed                        // 测试失败
)

// ProxyCandidate 代理候选者
type ProxyCandidate struct {
	IP     string
	Port   string
	State  ProxyState
	Result *ProxyInfo // 如果测试成功，存储结果
}

// BatchTestCoordinator 批量测试协调器
type BatchTestCoordinator struct {
	candidates map[string]*ProxyCandidate // key: "ip:port"
	config     *Config
	limiter    *Thread
	results    *TestResults
}
