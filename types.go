package main

import "time"

// ProxyInfo 存储代理信息
type ProxyInfo struct {
	IP          string
	Port        string
	User        string
	Password    string
	Latency     time.Duration
	Original    string
	TestResults map[string]bool // 记录各目标测试结果
	Quality     string          // "high", "medium", "low"
}

// TestResults 存储测试结果 - 只记录三种类型的代理
type TestResults struct {
	Normal       []ProxyInfo
	Multi        []ProxyInfo
	NoAuth       []ProxyInfo
	NoAuthHigh   []ProxyInfo // 高质量无认证代理
	NoAuthMedium []ProxyInfo // 中等质量无认证代理
}

// 测试目标配置
type TestTarget struct {
	Name    string
	Address string
	Timeout time.Duration
}
