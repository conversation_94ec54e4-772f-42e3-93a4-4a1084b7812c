package main

import "time"

// ProxyInfo 存储代理信息
type ProxyInfo struct {
	IP          string
	Port        string
	User        string
	Password    string
	Latency     time.Duration
	Original    string
	TestResults map[string]bool // 记录各目标测试结果
	Quality     string          // "high", "medium", "low"
}

// TestResults 存储测试结果 - 只记录三种类型的代理
type TestResults struct {
	High      []ProxyInfo // 高质量代理（所有目标都通过）
	Medium    []ProxyInfo // 中等质量代理（部分目标通过）
	Anonymous []ProxyInfo // 匿名代理（无认证凭据的代理）
}

// 测试目标配置
type TestTarget struct {
	Name    string
	Address string
	Timeout time.Duration
}
