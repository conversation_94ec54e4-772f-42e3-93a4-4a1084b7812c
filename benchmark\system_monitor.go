package main

import (
    "runtime"
    "time"
)

// getCPUUsage 获取CPU使用率（简化版本）
func (br *BenchmarkRunner) getCPUUsage() float64 {
    // Windows下的CPU监控实现
    var m1, m2 runtime.MemStats
    runtime.ReadMemStats(&m1)
    time.Sleep(100 * time.Millisecond)
    runtime.ReadMemStats(&m2)
    
    // 简化的CPU使用率估算
    return float64(runtime.NumGoroutine()) / float64(runtime.NumCPU()) * 10
}

// getMemoryUsage 获取内存使用量（MB）
func (br *BenchmarkRunner) getMemoryUsage() uint64 {
    var m runtime.MemStats
    runtime.ReadMemStats(&m)
    return m.Alloc / 1024 / 1024 // 转换为MB
}

// loadTestProxies 加载测试代理列表
func (br *BenchmarkRunner) loadTestProxies() error {
    // 从配置文件加载或生成测试数据
    testProxies := []ProxyCandidate{
        {IP: "127.0.0.1", Port: "1080"},     // 本地测试
        {IP: "*************", Port: "1080"}, // 局域网测试
        // 添加更多测试代理...
    }
    
    // 限制测试数量以确保测试可控
    maxTestCount := 50
    if len(testProxies) > maxTestCount {
        testProxies = testProxies[:maxTestCount]
    }
    
    br.testProxies = testProxies
    fmt.Printf("📋 加载了 %d 个测试代理\n", len(br.testProxies))
    return nil
}