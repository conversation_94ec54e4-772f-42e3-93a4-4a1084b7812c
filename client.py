import asyncio
import signal
import sys

class AsyncTestServer:
    def __init__(self, host='0.0.0.0', port=8080):
        self.host = host
        self.port = port
        self.running = True
        self.stats = {'connections': 0, 'errors': 0}
    
    async def handle_client(self, reader, writer):
        """异步处理客户端连接"""
        try:
            self.stats['connections'] += 1
            
            # 可选：读取少量数据
            try:
                await asyncio.wait_for(reader.read(100), timeout=0.1)
            except asyncio.TimeoutError:
                pass
            
            # 立即关闭连接
            writer.close()
            await writer.wait_closed()
            
        except Exception as e:
            self.stats['errors'] += 1
            print(f"连接处理错误: {e}")
    
    async def start_server(self):
        """启动异步服务器"""
        server = await asyncio.start_server(
            self.handle_client, 
            self.host, 
            self.port,
            limit=1000  # 支持1000个并发连接
        )
        
        print(f"高性能测试服务器启动: {self.host}:{self.port}")
        print("支持高并发代理测试...")
        
        # 定期打印统计信息
        asyncio.create_task(self.print_stats())
        
        async with server:
            await server.serve_forever()
    
    async def print_stats(self):
        """定期打印统计信息"""
        while self.running:
            await asyncio.sleep(60)  # 每分钟打印一次
            print(f"统计: 连接数={self.stats['connections']}, 错误数={self.stats['errors']}")

# 启动命令
async def main():
    server = AsyncTestServer()
    try:
        await server.start_server()
    except KeyboardInterrupt:
        print("\n服务器已停止")

if __name__ == "__main__":
    asyncio.run(main())