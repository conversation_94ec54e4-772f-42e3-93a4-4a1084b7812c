import asyncio
import signal
import sys
import time
import logging
from datetime import datetime
from collections import defaultdict
import traceback

class AsyncTestServer:
    def __init__(self, host='0.0.0.0', port=8080):
        self.host = host
        self.port = port
        self.running = True

        # 详细统计信息
        self.stats = {
            'total_connections': 0,
            'successful_connections': 0,
            'failed_connections': 0,
            'connection_errors': defaultdict(int),
            'start_time': time.time(),
            'last_connection_time': 0,
            'peak_concurrent': 0,
            'current_concurrent': 0
        }

        # 设置日志
        self.setup_logging()

    def setup_logging(self):
        """设置详细的日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler('proxy_test_server.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"代理测试服务器初始化 - {self.host}:{self.port}")

    def get_timestamp(self):
        """获取格式化的时间戳"""
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

    async def handle_client(self, reader, writer):
        """异步处理客户端连接 - 增强错误处理和调试信息"""
        client_addr = None
        connection_start = time.time()

        try:
            # 获取客户端地址
            client_addr = writer.get_extra_info('peername')
            self.stats['total_connections'] += 1
            self.stats['current_concurrent'] += 1
            self.stats['last_connection_time'] = connection_start

            # 更新峰值并发数
            if self.stats['current_concurrent'] > self.stats['peak_concurrent']:
                self.stats['peak_concurrent'] = self.stats['current_concurrent']

            self.logger.info(f"[{self.get_timestamp()}] 新连接: {client_addr} (并发: {self.stats['current_concurrent']})")

            # 尝试读取少量数据以验证连接
            try:
                data = await asyncio.wait_for(reader.read(100), timeout=0.5)
                if data:
                    self.logger.debug(f"收到数据: {len(data)} 字节 from {client_addr}")
            except asyncio.TimeoutError:
                self.logger.debug(f"读取超时 (正常): {client_addr}")
            except ConnectionResetError:
                self.logger.debug(f"客户端重置连接 (正常): {client_addr}")
            except Exception as read_error:
                self.logger.warning(f"读取数据异常: {read_error} from {client_addr}")

            # 发送简单响应（可选）
            try:
                response = b"HTTP/1.1 200 OK\r\nContent-Length: 2\r\n\r\nOK"
                writer.write(response)
                await writer.drain()
            except Exception as write_error:
                self.logger.debug(f"写入响应失败 (可忽略): {write_error}")

            # 安全关闭连接
            await self.safe_close_connection(writer, client_addr)

            # 记录成功连接
            self.stats['successful_connections'] += 1
            connection_duration = time.time() - connection_start
            self.logger.info(f"[{self.get_timestamp()}] 连接完成: {client_addr} (耗时: {connection_duration:.3f}s)")

        except ConnectionResetError as e:
            # Windows常见错误，客户端主动断开连接
            self.stats['connection_errors']['ConnectionReset'] += 1
            self.logger.debug(f"连接被重置 (正常): {client_addr} - {e}")

        except ConnectionAbortedError as e:
            # Windows网络中断
            self.stats['connection_errors']['ConnectionAborted'] += 1
            self.logger.debug(f"连接被中止 (正常): {client_addr} - {e}")

        except OSError as e:
            # 其他网络错误
            error_type = f"OSError_{e.errno}" if hasattr(e, 'errno') else "OSError_Unknown"
            self.stats['connection_errors'][error_type] += 1
            self.logger.warning(f"网络错误: {client_addr} - {e}")

        except Exception as e:
            # 其他未预期的错误
            self.stats['connection_errors']['Unexpected'] += 1
            self.logger.error(f"未预期错误: {client_addr} - {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")

        finally:
            # 确保并发计数正确
            self.stats['current_concurrent'] = max(0, self.stats['current_concurrent'] - 1)
            if self.stats['total_connections'] > self.stats['successful_connections']:
                self.stats['failed_connections'] = self.stats['total_connections'] - self.stats['successful_connections']

    async def safe_close_connection(self, writer, client_addr):
        """安全关闭连接，处理Windows特有的连接关闭问题"""
        try:
            if not writer.is_closing():
                writer.close()
                # 在Windows上，wait_closed可能抛出异常
                await asyncio.wait_for(writer.wait_closed(), timeout=1.0)
        except ConnectionResetError:
            # 客户端已经关闭连接，这是正常的
            self.logger.debug(f"连接已被客户端关闭: {client_addr}")
        except asyncio.TimeoutError:
            # 关闭超时，强制关闭
            self.logger.debug(f"关闭连接超时: {client_addr}")
        except Exception as e:
            # 其他关闭错误
            self.logger.debug(f"关闭连接时出错: {client_addr} - {e}")

    async def start_server(self):
        """启动异步服务器 - 增强版本"""
        try:
            server = await asyncio.start_server(
                self.handle_client,
                self.host,
                self.port,
                limit=2000,  # 支持2000个并发连接
                reuse_address=True,  # 允许地址重用
                reuse_port=True if sys.platform != 'win32' else False  # Windows不支持SO_REUSEPORT
            )

            self.logger.info(f"🚀 高性能代理测试服务器启动成功")
            self.logger.info(f"📍 监听地址: {self.host}:{self.port}")
            self.logger.info(f"⚡ 最大并发连接: 2000")
            self.logger.info(f"🖥️  运行平台: {sys.platform}")
            self.logger.info(f"🔧 地址重用: 已启用")

            # 启动统计信息任务
            asyncio.create_task(self.print_stats())
            asyncio.create_task(self.performance_monitor())

            # 设置优雅关闭
            self.setup_signal_handlers()

            async with server:
                await server.serve_forever()

        except OSError as e:
            self.logger.error(f"服务器启动失败: {e}")
            if e.errno == 10048:  # Windows: Address already in use
                self.logger.error(f"端口 {self.port} 已被占用，请选择其他端口")
            raise
        except Exception as e:
            self.logger.error(f"服务器启动异常: {e}")
            raise
    
    def setup_signal_handlers(self):
        """设置信号处理器用于优雅关闭"""
        if sys.platform != 'win32':
            # Unix系统支持信号处理
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)

    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备关闭服务器...")
        self.running = False

    async def print_stats(self):
        """定期打印详细统计信息"""
        while self.running:
            await asyncio.sleep(30)  # 每30秒打印一次

            uptime = time.time() - self.stats['start_time']
            uptime_str = f"{int(uptime//3600)}h{int((uptime%3600)//60)}m{int(uptime%60)}s"

            # 计算连接速率
            conn_rate = self.stats['total_connections'] / uptime if uptime > 0 else 0

            self.logger.info("=" * 60)
            self.logger.info(f"📊 服务器统计信息 (运行时间: {uptime_str})")
            self.logger.info(f"🔗 总连接数: {self.stats['total_connections']}")
            self.logger.info(f"✅ 成功连接: {self.stats['successful_connections']}")
            self.logger.info(f"❌ 失败连接: {self.stats['failed_connections']}")
            self.logger.info(f"⚡ 当前并发: {self.stats['current_concurrent']}")
            self.logger.info(f"📈 峰值并发: {self.stats['peak_concurrent']}")
            self.logger.info(f"🚀 连接速率: {conn_rate:.2f} conn/s")

            # 显示错误统计
            if self.stats['connection_errors']:
                self.logger.info("🔍 错误分类:")
                for error_type, count in self.stats['connection_errors'].items():
                    self.logger.info(f"   {error_type}: {count}")

            self.logger.info("=" * 60)

    async def performance_monitor(self):
        """性能监控任务"""
        last_connections = 0

        while self.running:
            await asyncio.sleep(10)  # 每10秒检查一次

            current_connections = self.stats['total_connections']
            new_connections = current_connections - last_connections

            if new_connections > 0:
                rate = new_connections / 10  # 每秒连接数
                self.logger.debug(f"⚡ 最近10秒: {new_connections} 连接 ({rate:.1f} conn/s)")

            last_connections = current_connections

# 启动命令
async def main():
    """主函数 - 启动增强版代理测试服务器"""
    print("🚀 启动增强版SOCKS5代理测试服务器...")
    print("=" * 50)

    server = AsyncTestServer()
    try:
        await server.start_server()
    except KeyboardInterrupt:
        server.logger.info("\n⏹️  收到键盘中断，正在关闭服务器...")
        server.running = False
    except Exception as e:
        server.logger.error(f"服务器运行异常: {e}")
        server.logger.error(f"异常详情: {traceback.format_exc()}")
    finally:
        # 打印最终统计
        uptime = time.time() - server.stats['start_time']
        server.logger.info("=" * 50)
        server.logger.info("📊 最终统计信息:")
        server.logger.info(f"🕐 运行时间: {uptime:.1f} 秒")
        server.logger.info(f"🔗 总连接数: {server.stats['total_connections']}")
        server.logger.info(f"✅ 成功率: {server.stats['successful_connections']/max(1,server.stats['total_connections'])*100:.1f}%")
        server.logger.info("🛑 服务器已停止")

if __name__ == "__main__":
    # 在Windows上设置事件循环策略以避免某些异步问题
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

    try:
        asyncio.run(main())
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)